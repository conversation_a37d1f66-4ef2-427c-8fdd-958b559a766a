package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.dto.AverageTimeCalculationConfig;
import tndung.vnfb.smm.entity.CalculationTimeAvgRun;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.repository.nontenant.CalculationTimeAvgRunRepository;
import tndung.vnfb.smm.repository.tenant.GSvRepository;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.service.AverageTimeCalculationService;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AverageTimeCalculationServiceImpl implements AverageTimeCalculationService {

    private final CalculationTimeAvgRunRepository calculationTimeAvgRunRepository;
    private final GSvRepository gServiceRepository;
    private final OrderRepository orderRepository;
    private final TenantRepository tenantRepository;

    @Override
    @Transactional
    public CalculationTimeAvgRun initializeCalculationRun(Long serviceId, String tenantId, AverageTimeCalculationConfig config) {
        // Check if calculation run already exists
        Optional<CalculationTimeAvgRun> existingRun = calculationTimeAvgRunRepository.findByServiceIdAndTenantId(serviceId, tenantId);

        if (existingRun.isPresent()) {
            return existingRun.get();
        }

        // Create new calculation run
        ZonedDateTime nextTimeToCalculate = ZonedDateTime.now().plusHours(config.getHoursInterval());
        CalculationTimeAvgRun calculationRun = new CalculationTimeAvgRun(serviceId, tenantId, nextTimeToCalculate);

        return calculationTimeAvgRunRepository.save(calculationRun);
    }

    @Override
    @Transactional
    public Long calculateAverageTime(Long serviceId, String tenantId, AverageTimeCalculationConfig config) {
        // Get or create calculation run
        CalculationTimeAvgRun calculationRun = getOrCreateCalculationRun(serviceId, tenantId, config);

        // Use new enhanced calculation logic
        return calculateAverageTimeEnhanced(calculationRun, config);
    }

    /**
     * Enhanced calculation logic with pending orders and buffer time
     */
    @Transactional
    public Long calculateAverageTimeEnhanced(CalculationTimeAvgRun calculationRun, AverageTimeCalculationConfig config) {
        Long serviceId = calculationRun.getServiceId();
        String tenantId = calculationRun.getTenantId();

        // Step 1: Get Y latest orders after X hours
        List<GOrder> latestOrders = getLatestOrdersForCalculation(serviceId, tenantId, config.getOrderCount());

        // Step 2: Check if orders meet priority conditions
        boolean meetsPriorityConditions = checkOrdersPriorityConditions(latestOrders, config);

        if (meetsPriorityConditions) {
            // Calculate immediately if conditions are met
            Long averageTimeSeconds = calculateAverageTimeForOrders(latestOrders);

            if (averageTimeSeconds != null) {
                // Update service and calculation run
                updateServiceAverageTime(serviceId, tenantId, averageTimeSeconds);
                calculationRun.setLastAverageTime(averageTimeSeconds);
                calculationRun.updateNextCalculationTime(config.getHoursInterval(), averageTimeSeconds);
                calculationRun.resetNoDataStatus();
                calculationRun.clearPendingOrders(); // Clear pending orders
                calculationTimeAvgRunRepository.save(calculationRun);

                log.info("Calculated average time immediately for service {} (tenant {}): {} seconds",
                    serviceId, tenantId, averageTimeSeconds);
                return averageTimeSeconds;
            }
        } else {
            // Add orders to pending list and schedule buffer calculation
            addOrdersToPendingList(calculationRun, latestOrders);
            scheduleBufferCalculation(calculationRun, config);

            log.info("Orders added to pending list for service {} (tenant {}). Buffer calculation scheduled.",
                serviceId, tenantId);
        }

        // Check if we should calculate with pending orders (during buffer time)
        if (shouldCalculateWithPendingOrders(calculationRun, config)) {
            return calculateWithPendingOrders(calculationRun, config);
        }

        return null;
    }

    @Override
    @Transactional
    public void processAllDueCalculations() {
        List<CalculationTimeAvgRun> dueCalculations = getDueCalculationRuns();

        for (CalculationTimeAvgRun calculationRun : dueCalculations) {
            try {
                String tenantId = calculationRun.getTenantId();
                TenantContext.setCurrentTenant(tenantId);

                AverageTimeCalculationConfig config = getConfigurationForTenant(tenantId);
                calculateAverageTime(calculationRun.getServiceId(), tenantId, config);

            } catch (Exception e) {
                log.error("Error processing calculation run for service {}: {}",
                    calculationRun.getServiceId(), e.getMessage(), e);
            } finally {
                TenantContext.clear();
            }
        }
    }

    @Override
    @Transactional
    public void processEarlyCalculations() {
        // Get configuration from main tenant
        Optional<Tenant> mainTenant = tenantRepository.findByMainIsTrue();
        if (mainTenant.isEmpty()) {
            return;
        }

        AverageTimeCalculationConfig config = getConfigurationForTenant(mainTenant.get().getId());
        List<CalculationTimeAvgRun> candidates = getEarlyCalculationCandidates(config.getHoursInterval());

        for (CalculationTimeAvgRun calculationRun : candidates) {
            try {
                String tenantId = calculationRun.getTenantId();
                TenantContext.setCurrentTenant(tenantId);

                // Check if service has sufficient data
                if (hasSufficientData(calculationRun.getServiceId(), tenantId, config.getOrderCount())) {
                    calculateAverageTime(calculationRun.getServiceId(), tenantId, config);
                }

            } catch (Exception e) {
                log.error("Error processing early calculation for service {}: {}",
                    calculationRun.getServiceId(), e.getMessage(), e);
            } finally {
                TenantContext.clear();
            }
        }
    }

    @Override
    public CalculationTimeAvgRun getOrCreateCalculationRun(Long serviceId, String tenantId, AverageTimeCalculationConfig config) {
        return calculationTimeAvgRunRepository.findByServiceIdAndTenantId(serviceId, tenantId)
            .orElseGet(() -> initializeCalculationRun(serviceId, tenantId, config));
    }

    @Override
    @Transactional
    public void updateServiceAverageTime(Long serviceId, String tenantId, Long averageTimeSeconds) {
        Optional<GService> serviceOpt = gServiceRepository.findById(serviceId);

        if (serviceOpt.isPresent()) {
            GService service = serviceOpt.get();
            service.setAverageTime(averageTimeSeconds);
            gServiceRepository.save(service);

            log.info("Updated average time for service {} to {} seconds", serviceId, averageTimeSeconds);
        } else {
            log.warn("Service {} not found for tenant {}", serviceId, tenantId);
        }
    }

    @Override
    public AverageTimeCalculationConfig getConfigurationForTenant(String tenantId) {
        Optional<Tenant> tenantOpt = tenantRepository.findById(tenantId);

        if (tenantOpt.isPresent()) {
            Tenant tenant = tenantOpt.get();
            String configJson = tenant.getAverageTimeSettings();

            if (configJson != null && !configJson.isEmpty()) {
                return AverageTimeCalculationConfig.fromJson(configJson);
            }
        }

        return AverageTimeCalculationConfig.getDefault();
    }

    @Override
    public boolean hasSufficientData(Long serviceId, String tenantId, int orderCount) {
        // Get recent orders for the service
        List<GOrder> recentOrders = orderRepository.findTopNByServiceIdAndTenantIdOrderByCreatedAtDesc(
            serviceId, tenantId, orderCount);

        if (recentOrders.size() < orderCount) {
            return false;
        }

        // Check if any order meets priority 1 or 2 criteria
        return recentOrders.stream().anyMatch(order -> {
            // Priority 1: Orders with processed quantity (quantity - remain) in range 990-1010
            int processedQuantity = order.getQuantity() - (order.getRemains() != null ? order.getRemains() : 0);
            boolean isPriority1 = processedQuantity >= 990 ;

            // Priority 2: Any completed order
            boolean isPriority2 = order.getStatus() == tndung.vnfb.smm.constant.enums.OrderStatus.COMPLETED;

            return isPriority1 || isPriority2;
        });
    }

    @Override
    public List<CalculationTimeAvgRun> getDueCalculationRuns() {
        return calculationTimeAvgRunRepository.findDueForCalculation(ZonedDateTime.now());
    }

    @Override
    public List<CalculationTimeAvgRun> getEarlyCalculationCandidates(int baseHoursInterval) {
        ZonedDateTime baseCalculationTime = ZonedDateTime.now().minusHours(baseHoursInterval);
        return calculationTimeAvgRunRepository.findEarlyCalculationCandidates(baseCalculationTime, ZonedDateTime.now());
    }

    @Override
    @Transactional
    public void handleNoDataScenario(CalculationTimeAvgRun calculationRun, AverageTimeCalculationConfig config) {
        if (calculationRun.getExtendedSearchCount() >= config.getMaxExtendedSearches()) {
            // Reset extended search count but keep no data flag
            calculationRun.setExtendedSearchCount(0);
            calculationRun.updateNextCalculationTime(config.getHoursInterval(), null);
        } else {
            calculationRun.markAsNoDataAndExtend(config.getHoursInterval());
        }

        calculationTimeAvgRunRepository.save(calculationRun);

        log.info("No data for service {} (tenant {}), extended search count: {}",
            calculationRun.getServiceId(), calculationRun.getTenantId(), calculationRun.getExtendedSearchCount());
    }

    @Override
    public Long calculateAverageTimeWithPriority(Long serviceId, String tenantId, int orderCount, int extendedSearchCount) {
        // Adjust order count if extended search is needed
        int totalOrderCount = orderCount;
        if (extendedSearchCount > 0) {
            totalOrderCount += extendedSearchCount;
        }

        // Get recent orders for the service
        List<GOrder> recentOrders = orderRepository.findTopNByServiceIdAndTenantIdOrderByCreatedAtDesc(
            serviceId, tenantId, totalOrderCount);

        if (recentOrders.isEmpty()) {
            return null;
        }

        // Priority 1: Orders with processed quantity (quantity - remain) in range 990-1010
        List<GOrder> priority1Orders = recentOrders.stream()
            .filter(order -> {
                int processedQuantity = order.getQuantity() - (order.getRemains() != null ? order.getRemains() : 0);
                return processedQuantity >= 990 ;
            })
            .limit(orderCount)
            .collect(Collectors.toList());

        if (!priority1Orders.isEmpty()) {
            return calculateAverageTimeForOrders(priority1Orders);
        }

        // Priority 2: Completed orders (scaled to 1000)
        List<GOrder> priority2Orders = recentOrders.stream()
            .filter(order -> order.getStatus().equals(OrderStatus.COMPLETED))
            .limit(orderCount)
            .collect(Collectors.toList());

        if (!priority2Orders.isEmpty()) {
            return calculateAverageTimeForOrders(priority2Orders);
        }

        // Priority 3: Orders with at least 50% completion (scaled and doubled)
        List<GOrder> priority3Orders = recentOrders.stream()
            .filter(order ->  {
                double completionRate = getCompletionPercentage(order);
                return is50Percent(completionRate);
            })
            .limit(orderCount)
            .collect(Collectors.toList());

        if (!priority3Orders.isEmpty()) {
            return calculateAverageTimeForOrders(priority3Orders);
        }

        // No data available
        return null;
    }

    @Override
    @Transactional
    public void cleanupStaleCalculationRuns() {
        ZonedDateTime thresholdTime = ZonedDateTime.now().minusDays(30);
        List<CalculationTimeAvgRun> staleRuns = calculationTimeAvgRunRepository.findStaleCalculationRuns(thresholdTime);

        if (!staleRuns.isEmpty()) {
            calculationTimeAvgRunRepository.deleteAll(staleRuns);
            log.info("Cleaned up {} stale calculation runs", staleRuns.size());
        }
    }

    @Override
    @Transactional
    public void initializeCalculationRunsForEnabledServices() {
        log.info("Starting optimized initialization of calculation runs for enabled services");

        try {
            // Get all tenants
            List<Tenant> tenants = tenantRepository.findAll();

            for (Tenant tenant : tenants) {
                String tenantId = tenant.getId();
                TenantContext.setCurrentTenant(tenantId);

                try {
                    int initializedCount = initializeCalculationRunsForTenant(tenantId);

                    if (initializedCount > 0) {
                        log.info("Initialized {} calculation runs for tenant {}", initializedCount, tenantId);
                    }

                } catch (Exception e) {
                    log.error("Error initializing calculation runs for tenant {}: {}",
                        tenantId, e.getMessage(), e);
                } finally {
                    TenantContext.clear();
                }
            }

            log.info("Completed optimized initialization of calculation runs for enabled services");

        } catch (Exception e) {
            log.error("Error in initializeCalculationRunsForEnabledServices: {}", e.getMessage(), e);
        }
    }

    /**
     * Initialize calculation runs for a specific tenant using optimized batch queries
     */
    private int initializeCalculationRunsForTenant(String tenantId) {
        // Get configuration for this tenant
        AverageTimeCalculationConfig config = getConfigurationForTenant(tenantId);

        // Use optimized query to get services that need calculation runs
        List<Long> serviceIdsNeedingRuns = gServiceRepository.findServiceIdsNeedingCalculationRuns(
            tndung.vnfb.smm.constant.enums.CommonStatus.ACTIVATED);

        if (serviceIdsNeedingRuns.isEmpty()) {
            log.debug("No services need calculation runs for tenant {}", tenantId);
            return 0;
        }

        // Batch create calculation runs
        List<CalculationTimeAvgRun> newRuns = new ArrayList<>();
        ZonedDateTime now = ZonedDateTime.now();

        for (Long serviceId : serviceIdsNeedingRuns) {
            CalculationTimeAvgRun run = CalculationTimeAvgRun.builder()
                .serviceId(serviceId)
                .nextTimeToCalculate(now.plusHours(config.getHoursInterval()))
                .ordersPending("[]") // Empty JSON array
                .lastCalculatedAt(null)
                .calculationCount(0)
                .lastAverageTime(null)
                .isNoData(false)
                .extendedSearchCount(0)
                .build();

            // Set tenant ID manually since it extends AbstractTenantEntity
            run.setTenantId(tenantId);

            newRuns.add(run);
        }

        // Batch save all calculation runs
        calculationTimeAvgRunRepository.saveAll(newRuns);

        log.debug("Batch created {} calculation runs for tenant {}", newRuns.size(), tenantId);
        return newRuns.size();
    }

    /**
     * Get latest orders for calculation
     */
    private List<GOrder> getLatestOrdersForCalculation(Long serviceId, String tenantId, int orderCount) {
        return orderRepository.findLatestOrdersForAverageTimeCalculation(serviceId, orderCount);
    }

    /**
     * Check if orders meet priority conditions for immediate calculation
     */
    private boolean checkOrdersPriorityConditions(List<GOrder> orders, AverageTimeCalculationConfig config) {
        if (orders.isEmpty()) {
            return false;
        }

        // Priority 1: All orders completed
        long completedCount = orders.stream()
            .mapToLong(order -> Math.max(0, order.getQuantity() - (order.getRemains() != null ? order.getRemains() : 0)))
            .sum();

        if (completedCount >= orders.size()) {
            log.debug("Priority 1 met: All {} orders completed", orders.size());
            return true;
        }

        // Priority 2: At least 50% completion rate
        double completionRate = (double) completedCount / orders.size();
        if (is50Percent(completionRate)) {
            log.debug("Priority 2 met: Completion rate {:.1f}% >= {:.1f}%",
                completionRate * 100, config.getMinCompletionPercentage());
            return true;
        }

        log.debug("Priority conditions not met. Completed: {}/{}, Rate: {:.1f}%",
            completedCount, orders.size(), completionRate * 100);
        return false;
    }

    /**
     * Add orders to pending list
     */
    private void addOrdersToPendingList(CalculationTimeAvgRun calculationRun, List<GOrder> orders) {
        for (GOrder order : orders) {
            calculationRun.addPendingOrderId(order.getId());
        }
        calculationTimeAvgRunRepository.save(calculationRun);

        log.debug("Added {} orders to pending list for service {}",
            orders.size(), calculationRun.getServiceId());
    }

    /**
     * Schedule buffer calculation (previous_average_time * 1.5)
     */
    private void scheduleBufferCalculation(CalculationTimeAvgRun calculationRun, AverageTimeCalculationConfig config) {
        Long previousAverageTime = calculationRun.getLastAverageTime();

        if (previousAverageTime != null) {
            // Calculate buffer time: previous_average_time * 1.5 (convert seconds to hours)
            double bufferHours = (previousAverageTime * config.getBufferMultiplier()) / 3600.0;
            ZonedDateTime bufferCalculationTime = ZonedDateTime.now().plusMinutes((long) (bufferHours * 60));

            // Update next calculation time to buffer time if it's sooner
            if (bufferCalculationTime.isBefore(calculationRun.getNextTimeToCalculate())) {
                calculationRun.setNextTimeToCalculate(bufferCalculationTime);
                calculationTimeAvgRunRepository.save(calculationRun);

                log.info("Scheduled buffer calculation for service {} at {} (buffer: {:.1f} hours)",
                    calculationRun.getServiceId(), bufferCalculationTime, bufferHours);
            }
        } else {
            log.debug("No previous average time available for service {}, using default schedule",
                calculationRun.getServiceId());
        }
    }

    /**
     * Check if should calculate with pending orders during buffer time
     */
    private boolean shouldCalculateWithPendingOrders(CalculationTimeAvgRun calculationRun, AverageTimeCalculationConfig config) {
        // Check if we're in buffer time and have pending orders
        return ZonedDateTime.now().isAfter(calculationRun.getNextTimeToCalculate()) &&
               calculationRun.getPendingOrderCount() > 0;
    }

    /**
     * Calculate average time using pending orders
     */
    private Long calculateWithPendingOrders(CalculationTimeAvgRun calculationRun, AverageTimeCalculationConfig config) {
        List<Long> pendingOrderIds = calculationRun.getPendingOrderIds();

        if (pendingOrderIds.isEmpty()) {
            return null;
        }

        // Get orders by IDs
        List<GOrder> pendingOrders = orderRepository.findAllById(pendingOrderIds);

        // Calculate average time
        Long averageTimeSeconds = calculateAverageTimeForOrders(pendingOrders);

        if (averageTimeSeconds != null) {
            // Update service and calculation run
            updateServiceAverageTime(calculationRun.getServiceId(), calculationRun.getTenantId(), averageTimeSeconds);
            calculationRun.setLastAverageTime(averageTimeSeconds);
            calculationRun.updateNextCalculationTime(config.getHoursInterval(), averageTimeSeconds);
            calculationRun.resetNoDataStatus();
            calculationRun.clearPendingOrders(); // Clear pending orders after calculation
            calculationTimeAvgRunRepository.save(calculationRun);

            log.info("Calculated average time with {} pending orders for service {}: {} seconds",
                pendingOrders.size(), calculationRun.getServiceId(), averageTimeSeconds);
            return averageTimeSeconds;
        }

        return null;
    }

    @Override
    @Transactional
    public void initializeCalculationRunForService(Long serviceId, String tenantId) {
        try {
            TenantContext.setCurrentTenant(tenantId);

            // Check if service is enabled
            Optional<GService> serviceOpt = gServiceRepository.findByIdAndTenantIdAndIsDeletedFalse(serviceId, tenantId);
            if (serviceOpt.isEmpty()) {
                log.warn("Service {} not found for tenant {}", serviceId, tenantId);
                return;
            }

            GService service = serviceOpt.get();
            if (!service.getStatus().equals(tndung.vnfb.smm.constant.enums.CommonStatus.ACTIVATED) ||
                service.getIsDeleted()) {
                log.debug("Service {} is not active or is deleted, skipping initialization", serviceId);
                return;
            }

            // Check if calculation run already exists
            Optional<CalculationTimeAvgRun> existingRun =
                calculationTimeAvgRunRepository.findByServiceIdAndTenantId(serviceId, tenantId);

            if (existingRun.isEmpty()) {
                AverageTimeCalculationConfig config = getConfigurationForTenant(tenantId);
                initializeCalculationRun(serviceId, tenantId, config);
                log.info("Initialized calculation run for service {} in tenant {}", serviceId, tenantId);
            }

        } catch (Exception e) {
            log.error("Error initializing calculation run for service {} in tenant {}: {}",
                serviceId, tenantId, e.getMessage(), e);
        } finally {
            TenantContext.clear();
        }
    }
    private boolean is50Percent(double completionPercentage) {
        return completionPercentage >= 49.0 && completionPercentage <= 51.0;
    }

    /**
     * Calculate minimum time for a list of orders
     */
    private Long calculateAverageTimeForOrders(List<GOrder> orders) {
        if (orders.isEmpty()) {
            return null;
        }

        long minSeconds = Long.MAX_VALUE;
        GOrder minTimeOrder = null;

        for (GOrder order : orders) {
            long orderTimeSeconds = calculateOrderTimeSeconds(order);
            if (orderTimeSeconds < minSeconds) {
                minSeconds = orderTimeSeconds;
                minTimeOrder = order;
            }
        }

        if (minTimeOrder == null) {
            return null;
        }

        // Check completion percentage and apply x2 if >= 50%
        double completionPercentage = getCompletionPercentage(minTimeOrder);
        if (is50Percent(completionPercentage)) {
            // If completion >= 50%, multiply time by 2
            return Math.round(minSeconds * 2.0);
        } else {
            // If completion < 50%, return original time
            return minSeconds;
        }
    }

    /**
     * Calculate time in seconds for an order
     */
    private long calculateOrderTimeSeconds(GOrder order) {
        ZonedDateTime startTime = order.getCreatedAt().atZoneSameInstant(java.time.ZoneOffset.UTC);
        ZonedDateTime endTime;

        if (order.getStatus().equals(OrderStatus.COMPLETED) && order.getCompletedAt() != null) {
            endTime = order.getCompletedAt().atZoneSameInstant(ZoneOffset.UTC);
        } else {
            endTime = ZonedDateTime.now(java.time.ZoneOffset.UTC);
        }

        return java.time.Duration.between(startTime, endTime).getSeconds();
    }

    /**
     * Get average quantity for a list of orders
     */
    private double getAverageQuantity(List<GOrder> orders) {
        if (orders.isEmpty()) {
            return 1000.0; // Default to avoid division by zero
        }

        long totalQuantity = orders.stream()
            .mapToLong(GOrder::getQuantity)
            .sum();

        return (double) totalQuantity / orders.size();
    }

    /**
     * Get completion percentage for an order
     */
    private double getCompletionPercentage(GOrder order) {
        // Implementation depends on your Order entity structure
        // This is a simplified example
        if (order.getStatus().name().equals("COMPLETED")) {
            return 100.0;
        }

        Integer remains = order.getRemains();
        Integer quantity = order.getQuantity();

        if (remains == null || quantity == null || quantity == 0) {
            return 0.0;
        }

        return 100.0 * (quantity - remains) / quantity;
    }
}
