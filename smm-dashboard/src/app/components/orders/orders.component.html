<!-- Theme-based conditional rendering -->
<ng-container *ngIf="currentTheme$ | async as currentTheme">
<div [ngSwitch]="currentTheme">
  <!-- Simple Theme -->
  <app-simple-orders *ngSwitchCase="LayoutTheme.SIMPLE"></app-simple-orders>

  <!-- Default Original Theme -->
  <div *ngSwitchDefault class="layout-container py-6 px-4">
    <!-- <h1 class="text-xl font-bold mb-2">Tìm kiếm đơn hàng</h1> -->

    <h2>{{ 'orders.search_orders' | translate }}</h2>

    <!-- Status filter tabs -->
    <div class="mb-6 ">
      <div class="flex flex-wrap -mb-px">
        <div *ngFor="let filter of statusFilters" class="mr-2">
          <button (click)="toggleFilter(filter)"
            [ngClass]="{'text-[var(--primary)] border-b-2 !border-[var(--primary)]' : filter.active, 'text-gray-500 hover:border-b-2  hover:text-gray-700 hover:border-gray-300': !filter.active}"
            class="inline-block p-4 border-transparent rounded-t-lg ">
            {{ filter.label }}
          </button>
        </div>

        <div class="ml-auto flex items-center">
          <button (click)="toggleFilters()" class="inline-flex items-center text-[var(--primary)]">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            {{ showFilters ? ('orders.hide' | translate) : ('orders.show' | translate) }}
          </button>
        </div>
      </div>
    </div>

  <!-- Search bar -->
  <div class="mb-6 flex flex-col sm:flex-row w-full gap-4 items-stretch">
    <div class="flex-grow">
      <app-search-box placeholder="{{ 'common.search' | translate }}" buttonText="{{ 'common.search' | translate }}" buttonPosition="right"
        containerClass="rounded-lg w-full h-[46px]" inputClass="bg-white text-gray-700 w-full h-full"
        buttonClass="bg-[var(--primary)] text-white font-medium hover:bg-cyan-600"
        (searchEvent)="onSearch($event)"></app-search-box>
    </div>
    <div class="flex">
      <button (click)="toggleFilters()"
        class="px-5 py-2 sm:py-0 w-full sm:w-auto h-full text-sm font-medium text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 flex items-center justify-center sm:justify-start flex-shrink-0">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
        </svg>
        {{ showFilters ? ('orders.hide_filters' | translate) : ('orders.show_filters' | translate) }}
      </button>
    </div>
  </div>

  <!-- Advanced filters -->
  <div *ngIf="showFilters" class="mb-6 p-4 bg-white border border-gray-200 rounded-lg ">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- Category dropdown -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-900">{{ 'orders.category' | translate }}</label>
        <app-icon-dropdown [options]="categories" [customClassDropdown]="'bg-white rounded-lg border'"
          [customClassButton]="' h-[46px]'" (selected)="onCategorySelected($event)"></app-icon-dropdown>
      </div>

      <!-- Service dropdown -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-900">{{ 'orders.service' | translate }}</label>
        <app-service-dropdown [lite]="true" [options]="services" [customClassDropdown]="'bg-white rounded-lg border '"
          [customClassButton]="' h-[46px]'" (selected)="onServiceSelected($event)"></app-service-dropdown>
      </div>

      <!-- Date range picker -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-900">{{ 'orders.date' | translate }}</label>

        <app-date-range-picker [containerClass]="'h-[46px]'" [(ngModel)]="dateRange" (dateRangeChanged)="onDateRangeChanged($event)"></app-date-range-picker>


      </div>
    </div>

    <div class="flex justify-between">
      <button (click)="applyFilters()"
        class="px-5 py-2.5 text-sm font-medium text-white bg-[var(--primary)] rounded-lg  focus:ring-4 ">
        {{ 'orders.apply' | translate }}
      </button>

      <button (click)="resetFilters()"
        class="flex items-center px-5 py-2.5 text-sm font-medium text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        {{ 'orders.reset_filter' | translate }}
      </button>
    </div>
  </div>

  <!-- View toggle buttons -->
  <!-- <div class="flex justify-end mb-4 viewMode-toggle">
    <div class="inline-flex rounded-md shadow-sm" role="group">
      <button type="button"
              (click)="viewMode = 'table'"
              [ngClass]="viewMode === 'table' ? 'bg-[var(--primary)] text-white' : 'bg-white text-gray-700 hover:bg-gray-100'"
              class="px-4 py-2 text-sm font-medium border border-gray-200 rounded-l-lg focus:z-10 focus:ring-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18M3 18h18M3 6h18" />
        </svg>
        {{ 'orders.table_view' | translate }}
      </button>
      <button type="button"
              (click)="viewMode = 'card'"
              [ngClass]="viewMode === 'card' ? 'bg-[var(--primary)] text-white' : 'bg-white text-gray-700 hover:bg-gray-100'"
              class="px-4 py-2 text-sm font-medium border border-gray-200 rounded-r-lg focus:z-10 focus:ring-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
        {{ 'orders.card_view' | translate }}
      </button>
    </div>
  </div> -->

  <!-- Order actions -->
  <div class="w-full">
    <!-- Bulk actions header -->
    <div class="bg-gray-50 p-3 border border-gray-200 rounded-t-lg mb-2">
      <div class="header-left">
        <div class="flex items-center gap-2 flex-wrap">
          <input id="default-checkbox" type="checkbox" [(ngModel)]="selectAll" (change)="toggleAllOrders()"
            class="checkbox-label">
          <span class="ms-2 text-sm font-medium">{{ 'orders.all_orders' | translate }}</span>
          <button class="bg-[var(--primary)] text-white px-4 py-2 rounded-lg" (click)="CopyID()">
            {{ 'common.copy' | translate }}
          </button>
          <button class="bg-[#6367FE] text-white px-4 py-2 rounded-lg" (click)="bulkRefillOrders()">
            {{ 'orders.refill' | translate }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading indicator -->
    <app-loading *ngIf="isLoading" [size]="'md'" [message]="'loading' | translate"></app-loading>

    <!-- No results message -->
    <div *ngIf="!isLoading && orders.length === 0" class="bg-white border p-4 text-center text-gray-500">
      {{ 'orders.no_orders_found' | translate }}
    </div>

    <!-- Table View -->
    <div *ngIf="!isLoading && orders.length > 0 && viewMode === 'table'" class="overflow-hidden overflow-x-auto">
      <table class="border border-gray-200 w-full">
        <thead class="text-xs text-gray-700 bg-gray-50">
          <tr>
            <th scope="col" class="p-4">
              <div class="flex items-center">
                <input type="checkbox" [(ngModel)]="selectAll" (change)="toggleAllOrders()"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
              </div>
            </th>
            <th scope="col" class="px-6 py-3">{{ 'orders.serial_number' | translate }}</th>
            <th scope="col" class="px-6 py-3">{{ 'orders.actions' | translate }}</th>
            <th scope="col" class="px-6 py-3">{{ 'orders.link' | translate }}</th>
            <th scope="col" class="px-6 py-3">{{ 'orders.remaining' | translate }}</th>
            <th scope="col" class="px-6 py-3">{{ 'orders.status' | translate }}</th>
            <th scope="col" class="px-6 py-3">{{ 'orders.notes' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <!-- Orders list -->
          <tr *ngFor="let order of orders; index as i" class="bg-white border-b hover:bg-gray-50">
            <td class="w-4 p-4">
              <div class="flex items-center">
                <input type="checkbox" [checked]="isOrderSelected(order.id)" (change)="toggleOrderSelection(order.id)"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
              </div>
            </td>
            <td class="w-4 px-3 py-2">{{ i + 1}}</td>
            <td class="px-3 py-2">
              <div class="flex flex-col gap-1 font-semibold">
                <button type="button" (click)="reorder(order)" class="text-[#007AFF] hover:text-cyan-800 bg-transparent border-none p-0 font-inherit cursor-pointer text-left">{{ 'orders.reorder' | translate }}</button>
                <!-- <a *ngIf="order.status.toLowerCase() !== 'canceled'" href="javascript:void(0)" class="text-[var(--primary)] hover:text-cyan-800">{{ 'orders.warranty' | translate }}</a> -->


                <button *ngIf="isRefill(order)"
                        type="button"
                        (click)="refillOrder(order)"
                        class="text-[#6367FE] hover:text-purple-800 bg-transparent border-none p-0 font-inherit cursor-pointer text-left"
                        [class.opacity-50]="order.loading || isRefillDisabled(order)"
                        [disabled]="order.loading || isRefillDisabled(order)"
                        [title]="isRefillDisabled(order) && order.refill_time_remaining ? 'Còn lại: ' + formatRefillTimeRemaining(order.refill_time_remaining) : ''">
                  <span *ngIf="!order.loading">{{ 'orders.refill' | translate }}</span>
                  <span *ngIf="order.loading">{{ 'common.loading' | translate }}</span>
                </button>

                <button *ngIf="isShowCancelButton(order)" type="button" (click)="cancelOrder(order)" class="text-[#E03137] hover:text-cyan-800 bg-transparent border-none p-0 font-inherit cursor-pointer text-left " [class.opacity-50]="order.loading" [disabled]="order.loading">
                  <span *ngIf="!order.loading">{{ 'orders.cancel' | translate }}</span>
                  <span *ngIf="order.loading">{{ 'common.loading' | translate }}</span>
                </button>
                <!-- <a *ngIf="order.service.cancel_button" href="javascript:void(0)" class="text-[#E03137] hover:text-cyan-800">{{ 'orders.cancel' | translate }}</a> -->
              </div>
            </td>
            <td class="px-3 py-2">
              <div class="link-content">
                <div class="text-gray-500 font-medium text-[14px] flex items-center gap-2">
                  ID: {{order.id}} | {{order.created_at | timezone:'short'}}
                  <span *ngIf="order.tag"
                        [ngClass]="{
                          'bg-yellow-100 text-yellow-800': order.tag.toLowerCase() === 'refill',
                          'bg-red-100 text-red-800': order.tag.toLowerCase() === 'cancel',
                          'bg-blue-100 text-blue-800': order.tag.toLowerCase() !== 'refill' && order.tag.toLowerCase() !== 'cancel'
                        }"
                        class="px-2 py-1 rounded text-xs font-medium">Request_{{order.tag | lowercase}}</span>
                  <button (click)="copySingleOrder(order)"
                          class="bg-[var(--primary)] text-white px-2 py-1 rounded text-xs font-medium hover:bg-cyan-600"
                          title="Copy order">
                    Copy
                  </button>
                </div>
                <div class="description-row text-[13px] font-semibold">
                  <div class="tag">{{order.service.id}}</div>
                  <span class="">{{order.service.name}} - <span class="text-red-500">{{order.service.price | currencyConvert}} </span></span>
                </div>
                <a [href]="order.link" target="_blank" class="link text-[13px] font-semibold">{{order.link}}</a>
              </div>
            </td>
            <td class="px-3 py-2">
              <div class="flex flex-col text-sm">
                <div class="flex gap-1">
                  <span class="text-gray-500">{{ 'orders.amount' | translate }}:</span>
                  <span class="text-red-500 font-medium">{{ order.actual_charge | currencyConvert }} </span>
                </div>
                <div class="flex gap-1">
                  <span class="text-gray-500">{{ 'orders.quantity' | translate }}:</span>
                  <span class="font-medium">{{ order.quantity }}</span>
                </div>
                <div class="flex gap-1">
                  <span class="text-gray-500">{{ 'orders.started' | translate }}:</span>
                  <span class="font-medium">{{ order.start_count }}</span>
                </div>
                <div class="flex gap-1">
                  <span class="text-gray-500">{{ 'orders.remaining_quantity' | translate }}:</span>
                  <span class="font-medium">{{ order.remains }}</span>
                </div>
              </div>
            </td>
            <td class="px-3 py-2">
              <span [ngClass]="'px-2 py-1 rounded-md text-xs font-medium ' + getStatusClass(order.status)">
                {{ order.status | titlecase }}
              </span>
            </td>
            <td class="px-3 py-2">
              <textarea
                class="w-full min-h-[60px] p-2 text-sm border border-gray-300 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                [value]="order.note || ''"
                (blur)="onNoteBlur(order, $event)"
                placeholder="Enter note...">
              </textarea>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Card View (for mobile) -->
    <div *ngIf="!isLoading && orders.length > 0 && viewMode === 'card'" class="grid grid-cols-1 gap-4">
      <div *ngFor="let order of orders; index as i" class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <!-- Card header with checkbox and ID -->
        <div class="flex justify-between items-center mb-3">
          <div class="flex items-center gap-2">
            <input type="checkbox" [checked]="isOrderSelected(order.id)" (change)="toggleOrderSelection(order.id)"
              class="w-4 h-4 mr-2 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
            <span class="text-gray-500 font-medium">ID: {{order.id}}</span>
            <!-- OrderTag display -->
            <span *ngIf="order.tag"
                  [ngClass]="{
                    'bg-yellow-100 text-yellow-800': order.tag.toLowerCase() === 'refill',
                    'bg-red-100 text-red-800': order.tag.toLowerCase() === 'cancel',
                    'bg-blue-100 text-blue-800': order.tag.toLowerCase() !== 'refill' && order.tag.toLowerCase() !== 'cancel'
                  }"
                  class="px-2 py-1 rounded text-xs font-medium">Request_{{order.tag | lowercase}}</span>
            <button (click)="copySingleOrder(order)"
                    class="bg-[var(--primary)] text-white px-2 py-1 rounded text-xs font-medium hover:bg-cyan-600"
                    title="Copy order">
              Copy
            </button>
          </div>
          <span [ngClass]="'px-2 py-1 rounded-md text-xs font-medium ' + getStatusClass(order.status)">
            {{ order.status | titlecase }}
          </span>
        </div>

        <!-- Service info -->
        <div class="mb-3">
          <div class="font-semibold text-[15px] mb-1">{{order.service.name}}</div>
          <div class="text-gray-500 text-sm">{{order.created_at | date:'short'}}</div>
          <div class="text-red-500 font-medium text-sm">{{order.charge | currencyConvert}} <span *ngIf="order.actual_charge && order.actual_charge !== order.charge">({{order.actual_charge | currencyConvert}})</span></div>
        </div>

        <!-- Link -->
        <div class="mb-3 bg-gray-50 p-2 rounded text-sm break-words">
          {{order.link}}
        </div>

        <!-- Order details -->
        <div class="grid grid-cols-2 gap-2 mb-3 text-sm">
          <div>
            <span class="text-gray-500">{{ 'orders.quantity' | translate }}:</span>
            <span class="font-medium ml-1">{{ order.quantity }}</span>
          </div>
          <div>
            <span class="text-gray-500">{{ 'orders.started' | translate }}:</span>
            <span class="font-medium ml-1">{{ order.start_count }}</span>
          </div>
          <div>
            <span class="text-gray-500">{{ 'orders.remaining_quantity' | translate }}:</span>
            <span class="font-medium ml-1">{{ order.remains }}</span>
          </div>
          <div>
            <span class="text-gray-500">API Order ID:</span>
            <span class="font-medium ml-1">{{ order.api_order_id || '-' }}</span>
          </div>
        </div>

        <!-- Notes section -->
        <div class="mb-3">
          <label class="block text-gray-500 text-sm mb-1">{{ 'orders.notes' | translate }}:</label>
          <textarea
            class="w-full min-h-[60px] p-2 text-sm border border-gray-300 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            [value]="order.note || ''"
            (blur)="onNoteBlur(order, $event)"
            placeholder="Enter note...">
          </textarea>
        </div>

        <!-- Action buttons -->
        <div class="flex gap-2 mt-2">
          <button (click)="reorder(order)" class="flex-1 text-center py-2 text-sm font-medium text-white bg-[#007AFF] rounded-lg hover:bg-blue-600">
            {{ 'orders.reorder' | translate }}
          </button>

          <button *ngIf="order.service.refill"
                  (click)="refillOrder(order)"
                  class="flex-1 text-center py-2 text-sm font-medium text-white bg-[#6367FE] rounded-lg hover:bg-purple-600"
                  [disabled]="order.loading || isRefillDisabled(order)"
                  [class.opacity-50]="order.loading || isRefillDisabled(order)"
                  [title]="isRefillDisabled(order) && order.refill_time_remaining ? 'Còn lại: ' + formatRefillTimeRemaining(order.refill_time_remaining) : ''">
            <span *ngIf="!order.loading">{{ 'orders.refill' | translate }}</span>
            <span *ngIf="order.loading">{{ 'common.loading' | translate }}</span>
          </button>
          <button *ngIf="isShowCancelButton(order)" (click)="cancelOrder(order)" class="flex-1 text-center py-2 text-sm font-medium text-white bg-[#E03137] rounded-lg hover:bg-red-600" [disabled]="order.loading" [class.opacity-50]="order.loading">
            <span *ngIf="!order.loading">{{ 'orders.cancel' | translate }}</span>
            <span *ngIf="order.loading">{{ 'common.loading' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="pagination" *ngIf="!isLoading && pagination.totalElements > 0">
    <div class="page-numbers">
        <button class="page-nav" [disabled]="pagination.pageNumber === 0" (click)="goToPage(pagination.pageNumber - 1)">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </button>

        <!-- First page (always visible) -->
        <button class="page-num" [ngClass]="{'active': pagination.pageNumber === 0}" (click)="goToPage(0)">1</button>

        <!-- Ellipsis for gap at the beginning (hidden on mobile if not needed) -->
        <button *ngIf="pagination.pageNumber > 3" class="page-num hidden md:block">...</button>

        <!-- Pages before current (limited on mobile) -->
        <ng-container *ngFor="let page of getPageRange()">
            <button *ngIf="page !== 0 && page !== pagination.totalPages - 1"
                    class="page-num"
                    [ngClass]="{
                      'active': pagination.pageNumber === page,
                      'hidden sm:block': page !== pagination.pageNumber && page !== pagination.pageNumber - 1 && page !== pagination.pageNumber + 1
                    }"
                    (click)="goToPage(page)">
                {{ page + 1 }}
            </button>
        </ng-container>

        <!-- Ellipsis for gap at the end (hidden on mobile if not needed) -->
        <button *ngIf="pagination.pageNumber < pagination.totalPages - 4" class="page-num hidden md:block">...</button>

        <!-- Last page (if more than one page) -->
        <button *ngIf="pagination.totalPages > 1"
                class="page-num"
                [ngClass]="{'active': pagination.pageNumber === pagination.totalPages - 1}"
                (click)="goToPage(pagination.totalPages - 1)">
            {{ pagination.totalPages }}
        </button>

        <button class="page-nav" [disabled]="pagination.pageNumber >= pagination.totalPages - 1" (click)="goToPage(pagination.pageNumber + 1)">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>

    <div class="page-info">
        <div class="text-xs text-gray-500">
          {{ 'orders.showing' | translate }} {{ pagination.pageNumber * pagination.pageSize + 1 }} -
          {{ (pagination.pageNumber + 1) * pagination.pageSize > pagination.totalElements ?
             pagination.totalElements :
             (pagination.pageNumber + 1) * pagination.pageSize }}
          {{ 'orders.of' | translate }} {{ pagination.totalElements }} {{ 'orders.entries' | translate }}
        </div>

        <div class="show-entries">
            <select [(ngModel)]="pagination.pageSize" (change)="changePageSize()" class="text-xs">
                <option [value]="10">10</option>
                <option [value]="25">25</option>
                <option [value]="50">50</option>
                <option [value]="100">100</option>
            </select>
        </div>
    </div>
</div>

    <!-- Bulk Refill Confirmation Modal -->
    <app-delete-confirmation
      *ngIf="showBulkRefillConfirmation"
      [itemName]="'eligible orders for refill'"
      [isLoading]="isBulkRefilling"
      (close)="closeBulkRefillConfirmation()"
      (confirm)="confirmBulkRefill()">
    </app-delete-confirmation>
  </div>
</div>
</ng-container>